import React from 'react'
import { cn } from '@/lib/utils'

const SimpleBarChart = ({ data, className, height = 200 }) => {
  if (!data || data.length === 0) {
    return (
      <div className={cn("flex items-center justify-center", className)} style={{ height }}>
        <p className="text-muted-foreground">No data available</p>
      </div>
    )
  }

  const maxValue = Math.max(...data.map(item => item.value))
  const barWidth = 100 / data.length

  return (
    <div className={cn("w-full", className)} style={{ height }}>
      <svg width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="none">
        {data.map((item, index) => {
          const barHeight = (item.value / maxValue) * 80 // 80% of chart height
          const x = index * barWidth
          const y = 100 - barHeight - 10 // 10% margin from bottom
          
          return (
            <rect
              key={index}
              x={`${x + barWidth * 0.1}%`}
              y={`${y}%`}
              width={`${barWidth * 0.8}%`}
              height={`${barHeight}%`}
              className="fill-primary/80 hover:fill-primary transition-colors"
              rx="1"
            />
          )
        })}
      </svg>
      <div className="flex justify-between mt-2 text-xs text-muted-foreground">
        {data.map((item, index) => (
          <span key={index} className="text-center" style={{ width: `${barWidth}%` }}>
            {item.label}
          </span>
        ))}
      </div>
    </div>
  )
}

const SimpleLineChart = ({ data, className, height = 200 }) => {
  if (!data || data.length === 0) {
    return (
      <div className={cn("flex items-center justify-center", className)} style={{ height }}>
        <p className="text-muted-foreground">No data available</p>
      </div>
    )
  }

  const maxValue = Math.max(...data.map(item => item.value))
  const minValue = Math.min(...data.map(item => item.value))
  const range = maxValue - minValue || 1

  const points = data.map((item, index) => {
    const x = (index / (data.length - 1)) * 90 + 5 // 5% margin on each side
    const y = 90 - ((item.value - minValue) / range) * 80 // 80% of chart height, 10% margin top/bottom
    return `${x},${y}`
  }).join(' ')

  return (
    <div className={cn("w-full", className)} style={{ height }}>
      <svg width="100%" height="100%" viewBox="0 0 100 100" className="overflow-visible">
        {/* Grid lines */}
        <defs>
          <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
            <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" strokeWidth="0.5" className="text-muted-foreground/20"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#grid)" />
        
        {/* Line */}
        <polyline
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          points={points}
          className="text-primary"
        />
        
        {/* Data points */}
        {data.map((item, index) => {
          const x = (index / (data.length - 1)) * 90 + 5
          const y = 90 - ((item.value - minValue) / range) * 80
          return (
            <circle
              key={index}
              cx={x}
              cy={y}
              r="2"
              className="fill-primary stroke-background stroke-2"
            />
          )
        })}
      </svg>
      <div className="flex justify-between mt-2 text-xs text-muted-foreground">
        {data.map((item, index) => (
          <span key={index} className="text-center">
            {item.label}
          </span>
        ))}
      </div>
    </div>
  )
}

const SimpleAreaChart = ({ data, className, height = 200 }) => {
  if (!data || data.length === 0) {
    return (
      <div className={cn("flex items-center justify-center", className)} style={{ height }}>
        <p className="text-muted-foreground">No data available</p>
      </div>
    )
  }

  const maxValue = Math.max(...data.map(item => item.value))
  const minValue = Math.min(...data.map(item => item.value))
  const range = maxValue - minValue || 1

  const points = data.map((item, index) => {
    const x = (index / (data.length - 1)) * 90 + 5
    const y = 90 - ((item.value - minValue) / range) * 80
    return `${x},${y}`
  }).join(' ')

  const areaPoints = `5,90 ${points} ${95},90`

  return (
    <div className={cn("w-full", className)} style={{ height }}>
      <svg width="100%" height="100%" viewBox="0 0 100 100">
        {/* Area */}
        <polygon
          points={areaPoints}
          className="fill-primary/20 stroke-primary stroke-2"
          strokeLinejoin="round"
        />
        
        {/* Data points */}
        {data.map((item, index) => {
          const x = (index / (data.length - 1)) * 90 + 5
          const y = 90 - ((item.value - minValue) / range) * 80
          return (
            <circle
              key={index}
              cx={x}
              cy={y}
              r="2"
              className="fill-primary stroke-background stroke-2"
            />
          )
        })}
      </svg>
      <div className="flex justify-between mt-2 text-xs text-muted-foreground">
        {data.map((item, index) => (
          <span key={index} className="text-center">
            {item.label}
          </span>
        ))}
      </div>
    </div>
  )
}

export { SimpleBarChart, SimpleLineChart, SimpleAreaChart }
