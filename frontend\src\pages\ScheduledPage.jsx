import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { format } from 'date-fns'
import { cn } from '@/lib/utils'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Alert } from '@/components/ui/alert'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Clock,
  Trash2,
  Edit,
  Plus,
  Calendar,
} from 'lucide-react'

import { useWhatsAppStore } from '../stores/whatsappStore'

const ScheduledPage = () => {
  const { isAuthenticated } = useWhatsAppStore()
  const [openDialog, setOpenDialog] = useState(false)
  const [scheduledMessages] = useState([]) // This would come from API

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm()

  const handleCreateScheduled = (data) => {
    console.log('Creating scheduled message:', data)
    setOpenDialog(false)
    reset()
  }

  const handleDeleteScheduled = (id) => {
    console.log('Deleting scheduled message:', id)
  }

  if (!isAuthenticated) {
    return (
      <div className="space-y-6">
        <Alert className="border-yellow-200 bg-yellow-50 text-yellow-800">
          WhatsApp is not authenticated. Please scan the QR code on the dashboard first.
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">Scheduled Messages</h1>
          <p className="text-muted-foreground">
            Manage your scheduled and recurring messages
          </p>
        </div>
        <Dialog open={openDialog} onOpenChange={setOpenDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Schedule Message
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Schedule New Message</DialogTitle>
              <DialogDescription>
                Create a new scheduled or recurring message
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit(handleCreateScheduled)} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="chat">Chat</Label>
                <Input
                  id="chat"
                  placeholder="Enter chat ID or phone number"
                  {...register('chat', { required: 'Chat is required' })}
                />
                {errors.chat && (
                  <p className="text-sm text-red-600">{errors.chat.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="message">Message</Label>
                <Input
                  id="message"
                  placeholder="Enter your message"
                  {...register('message', { required: 'Message is required' })}
                />
                {errors.message && (
                  <p className="text-sm text-red-600">{errors.message.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="scheduleTime">Schedule Time</Label>
                <Input
                  id="scheduleTime"
                  type="datetime-local"
                  {...register('scheduleTime', { required: 'Schedule time is required' })}
                />
                {errors.scheduleTime && (
                  <p className="text-sm text-red-600">{errors.scheduleTime.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">Type</Label>
                <Select {...register('type')}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select message type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="once">Send Once</SelectItem>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setOpenDialog(false)}>
                  Cancel
                </Button>
                <Button type="submit">
                  Schedule Message
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Scheduled Messages Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Scheduled Messages
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Chat</TableHead>
                <TableHead>Message</TableHead>
                <TableHead>Schedule Time</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {scheduledMessages.length > 0 ? (
                scheduledMessages.map((message) => (
                  <TableRow key={message.id}>
                    <TableCell className="font-medium">{message.chatId}</TableCell>
                    <TableCell>
                      <div className="max-w-[200px] truncate text-sm">
                        {message.message}
                      </div>
                    </TableCell>
                    <TableCell>
                      {format(new Date(message.scheduleTime), 'MMM dd, yyyy HH:mm')}
                    </TableCell>
                    <TableCell>
                      <Badge variant={message.recurring ? "secondary" : "default"}>
                        {message.recurring ? 'Recurring' : 'One-time'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          message.status === 'sent' ? 'default' :
                          message.status === 'failed' ? 'destructive' : 'outline'
                        }
                      >
                        {message.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteScheduled(message.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} className="text-center">
                    <div className="flex flex-col items-center justify-center p-8 text-muted-foreground">
                      <Clock className="h-16 w-16 mb-4 opacity-50" />
                      <p className="text-lg font-medium">No scheduled messages</p>
                      <p className="text-sm">
                        Create your first scheduled message to get started
                      </p>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

    </div>
  )
}

export default ScheduledPage
