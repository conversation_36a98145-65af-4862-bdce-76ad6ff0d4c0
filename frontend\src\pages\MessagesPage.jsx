import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { format } from 'date-fns'
import { cn } from '@/lib/utils'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Alert } from '@/components/ui/alert'
import { Avatar } from '@/components/ui/avatar'
import { Separator } from '@/components/ui/separator'
import {
  Send,
  Paperclip,
  Search,
  RefreshCw,
  MessageSquare,
} from 'lucide-react'

import { useWhatsAppStore } from '../stores/whatsappStore'

const MessagesPage = () => {
  const {
    isReady,
    isAuthenticated,
    messages,
    currentChat,
    chats,
    contacts,
    isLoadingMessages,
    setCurrentChat,
    sendMessage,
    loadChats,
    getContactName,
  } = useWhatsAppStore()

  const [searchTerm, setSearchTerm] = useState('')
  const [selectedFile, setSelectedFile] = useState(null)

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm()

  useEffect(() => {
    if (isReady && chats.length === 0) {
      loadChats()
    }
  }, [isReady, chats.length, loadChats])

  const filteredChats = chats.filter(chat =>
    chat.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    chat.id.includes(searchTerm)
  )

  const currentChatMessages = messages.filter(msg =>
    msg.from === currentChat || msg.to === currentChat
  )

  const onSendMessage = async (data) => {
    if (!currentChat) {
      alert('Please select a chat first')
      return
    }

    try {
      await sendMessage(currentChat, data.message)
      reset()
    } catch (error) {
      console.error('Failed to send message:', error)
    }
  }

  const handleFileSelect = (event) => {
    const file = event.target.files[0]
    setSelectedFile(file)
  }

  const formatMessageTime = (timestamp) => {
    return format(new Date(timestamp * 1000), 'HH:mm')
  }

  if (!isAuthenticated) {
    return (
      <Box>
        <Alert severity="warning">
          WhatsApp is not authenticated. Please scan the QR code on the dashboard first.
        </Alert>
      </Box>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Messages</h1>
        <p className="text-muted-foreground">
          Send and receive WhatsApp messages
        </p>
      </div>

      {/* Status Alert */}
      {!isReady && (
        <Alert className="border-yellow-200 bg-yellow-50 text-yellow-800">
          WhatsApp client is not ready. Please wait for connection to be established.
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
        {/* Chat List */}
        <div className="md:col-span-1">
          <Card className="h-full flex flex-col">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Chats</CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={loadChats}
                  className="h-8 w-8 p-0"
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="flex-1 flex flex-col space-y-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search chats..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              <div className="flex-1 overflow-auto space-y-2">
                {filteredChats.map((chat) => (
                  <div
                    key={chat.id}
                    className={cn(
                      "flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors",
                      currentChat === chat.id
                        ? "bg-primary/10 border border-primary/20"
                        : "hover:bg-muted/50"
                    )}
                    onClick={() => setCurrentChat(chat.id)}
                  >
                    <Avatar className="h-10 w-10">
                      <div className="flex h-full w-full items-center justify-center bg-primary text-primary-foreground text-sm font-medium">
                        {chat.name?.charAt(0) || chat.id.charAt(0)}
                      </div>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">
                        {chat.name || getContactName(chat.id)}
                      </p>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-muted-foreground">
                          {chat.isGroup ? 'Group' : 'Contact'}
                        </span>
                        {chat.unreadCount > 0 && (
                          <Badge variant="default" className="h-5 text-xs">
                            {chat.unreadCount}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Chat Area */}
        <div className="md:col-span-2">
          <Card className="h-full flex flex-col">
            {currentChat ? (
              <>
                {/* Chat Header */}
                <CardHeader className="border-b">
                  <div>
                    <CardTitle className="text-lg">
                      {getContactName(currentChat)}
                    </CardTitle>
                    <p className="text-sm text-muted-foreground">
                      {currentChat}
                    </p>
                  </div>
                </CardHeader>

                {/* Messages Area */}
                <CardContent className="flex-1 overflow-auto p-4">
                  {isLoadingMessages ? (
                    <div className="flex justify-center p-8">
                      <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                    </div>
                  ) : currentChatMessages.length > 0 ? (
                    <div className="space-y-4">
                      {currentChatMessages.map((message, index) => (
                        <div
                          key={index}
                          className={cn(
                            "flex",
                            message.fromMe ? "justify-end" : "justify-start"
                          )}
                        >
                          <div
                            className={cn(
                              "max-w-[70%] rounded-lg p-3",
                              message.fromMe
                                ? "bg-primary text-primary-foreground rounded-br-none"
                                : "bg-muted rounded-bl-none"
                            )}
                          >
                            <p className="text-sm">
                              {message.body}
                            </p>
                            <p className={cn(
                              "text-xs mt-1",
                              message.fromMe ? "text-primary-foreground/70" : "text-muted-foreground"
                            )}>
                              {formatMessageTime(message.timestamp)}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-full text-muted-foreground">
                      <div className="text-center">
                        <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No messages yet. Start a conversation!</p>
                      </div>
                    </div>
                  )}
                </CardContent>

                {/* Message Input */}
                <div className="border-t p-4">
                  <form
                    onSubmit={handleSubmit(onSendMessage)}
                    className="flex items-end space-x-2"
                  >
                    <div className="flex-1">
                      <Input
                        placeholder="Type a message..."
                        disabled={!isReady}
                        className="min-h-[40px] resize-none"
                        {...register('message', {
                          required: 'Message is required',
                        })}
                      />
                      {errors.message && (
                        <p className="text-sm text-red-600 mt-1">{errors.message.message}</p>
                      )}
                    </div>
                    <input
                      type="file"
                      id="file-input"
                      className="hidden"
                      onChange={handleFileSelect}
                      accept="image/*,video/*,audio/*,.pdf,.doc,.docx"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      disabled={!isReady}
                      onClick={() => document.getElementById('file-input').click()}
                    >
                      <Paperclip className="h-4 w-4" />
                    </Button>
                    <Button
                      type="submit"
                      disabled={!isReady}
                      className="px-6"
                    >
                      <Send className="h-4 w-4 mr-2" />
                      Send
                    </Button>
                  </form>
                  {selectedFile && (
                    <p className="text-sm text-muted-foreground mt-2">
                      Selected: {selectedFile.name}
                    </p>
                  )}
                </div>
              </>
            ) : (
              <div className="flex items-center justify-center h-full text-muted-foreground">
                <div className="text-center">
                  <MessageSquare className="h-16 w-16 mx-auto mb-4 opacity-50" />
                  <p className="text-lg">Select a chat to start messaging</p>
                </div>
              </div>
            )}
          </Card>
        </div>
      </div>
    </div>
  )
}

export default MessagesPage
