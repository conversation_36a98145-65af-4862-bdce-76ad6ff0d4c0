import React, { useEffect, useState } from 'react'
import { cn } from '@/lib/utils'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { Separator } from '@/components/ui/separator'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { SimpleLine<PERSON>hart, SimpleBarChart } from '@/components/ui/simple-chart'
import {
  MessageSquare,
  Users,
  UsersRound,
  Activity,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock,
  Send,
} from 'lucide-react'

import { useWhatsAppStore } from '../stores/whatsappStore'
import { useSocketStore } from '../stores/socketStore'
import QRCodeSection from '../components/QRCodeSection'

const ModernDashboardPage = () => {
  const {
    isReady,
    isAuthenticated,
    qrCode,
    connectionState,
    error,
    contacts,
    chats,
    messages,
    requestQRCode,
    isLoadingQR,
  } = useWhatsAppStore()
  
  const { isConnected } = useSocketStore()
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Request QR code if not authenticated
    if (!isAuthenticated && !qrCode) {
      requestQRCode()
    }
    
    // Simulate loading state
    const timer = setTimeout(() => setIsLoading(false), 1000)
    return () => clearTimeout(timer)
  }, [isAuthenticated, qrCode, requestQRCode])

  const getStatusVariant = () => {
    if (!isConnected) return 'destructive'
    if (isReady && isAuthenticated) return 'default'
    if (isAuthenticated) return 'secondary'
    return 'outline'
  }

  const getStatusText = () => {
    if (!isConnected) return 'Server Disconnected'
    if (isReady && isAuthenticated) return 'WhatsApp Ready'
    if (isAuthenticated) return 'WhatsApp Connecting'
    return 'WhatsApp Not Authenticated'
  }

  const getStatusIcon = () => {
    if (!isConnected) return AlertCircle
    if (isReady && isAuthenticated) return CheckCircle
    if (isAuthenticated) return Clock
    return AlertCircle
  }

  const StatusIcon = getStatusIcon()

  const stats = [
    {
      title: 'Total Messages',
      value: messages.length,
      change: '+12%',
      trend: 'up',
      icon: MessageSquare,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 dark:bg-blue-950',
    },
    {
      title: 'Active Contacts',
      value: contacts.length,
      change: '+5%',
      trend: 'up',
      icon: Users,
      color: 'text-green-600',
      bgColor: 'bg-green-50 dark:bg-green-950',
    },
    {
      title: 'Group Chats',
      value: chats.filter(chat => chat.isGroup).length,
      change: '+2%',
      trend: 'up',
      icon: UsersRound,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50 dark:bg-purple-950',
    },
    {
      title: 'Messages Today',
      value: messages.filter(msg => {
        const today = new Date().toDateString()
        return new Date(msg.timestamp).toDateString() === today
      }).length,
      change: '-3%',
      trend: 'down',
      icon: Activity,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50 dark:bg-orange-950',
    },
  ]

  const recentMessages = messages.slice(-5).reverse()

  // Generate sample chart data
  const messageChartData = [
    { label: 'Mon', value: 12 },
    { label: 'Tue', value: 19 },
    { label: 'Wed', value: 8 },
    { label: 'Thu', value: 15 },
    { label: 'Fri', value: 22 },
    { label: 'Sat', value: 18 },
    { label: 'Sun', value: 14 },
  ]

  const activityChartData = [
    { label: 'Jan', value: 65 },
    { label: 'Feb', value: 78 },
    { label: 'Mar', value: 52 },
    { label: 'Apr', value: 89 },
    { label: 'May', value: 95 },
    { label: 'Jun', value: 87 },
  ]

  const MetricCard = ({ stat, isLoading }) => {
    const Icon = stat.icon
    const TrendIcon = stat.trend === 'up' ? TrendingUp : TrendingDown
    
    if (isLoading) {
      return (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-8 w-16" />
                <Skeleton className="h-3 w-12" />
              </div>
              <Skeleton className="h-12 w-12 rounded-lg" />
            </div>
          </CardContent>
        </Card>
      )
    }

    return (
      <Card className="transition-all duration-200 ease-in-out hover:shadow-md">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">
                {stat.title}
              </p>
              <p className="text-3xl font-bold">
                {stat.value.toLocaleString()}
              </p>
              <div className="flex items-center gap-1 text-xs">
                <TrendIcon className={cn(
                  "h-3 w-3",
                  stat.trend === 'up' ? "text-green-600" : "text-red-600"
                )} />
                <span className={cn(
                  "font-medium",
                  stat.trend === 'up' ? "text-green-600" : "text-red-600"
                )}>
                  {stat.change}
                </span>
                <span className="text-muted-foreground">from last month</span>
              </div>
            </div>
            <div className={cn(
              "flex h-12 w-12 items-center justify-center rounded-lg",
              stat.bgColor
            )}>
              <Icon className={cn("h-6 w-6", stat.color)} />
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Monitor your WhatsApp API gateway status and activity
          </p>
        </div>
        <Button onClick={requestQRCode} disabled={isLoadingQR}>
          <RefreshCw className={cn("mr-2 h-4 w-4", isLoadingQR && "animate-spin")} />
          Refresh
        </Button>
      </div>

      {/* Status Alert */}
      <Card className={cn(
        "border-l-4",
        !isConnected ? "border-l-red-500" :
        isReady && isAuthenticated ? "border-l-green-500" :
        isAuthenticated ? "border-l-yellow-500" : "border-l-gray-500"
      )}>
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <StatusIcon className={cn(
              "h-5 w-5",
              !isConnected ? "text-red-500" :
              isReady && isAuthenticated ? "text-green-500" :
              isAuthenticated ? "text-yellow-500" : "text-gray-500"
            )} />
            <div className="flex-1">
              <p className="font-medium">{getStatusText()}</p>
              <p className="text-sm text-muted-foreground">
                {getStatusText() === 'WhatsApp Ready' 
                  ? 'WhatsApp is connected and ready to send/receive messages'
                  : getStatusText() === 'WhatsApp Connecting'
                  ? 'WhatsApp is connecting, please wait...'
                  : getStatusText() === 'Server Disconnected'
                  ? 'Connection to server lost. Please check your internet connection.'
                  : 'WhatsApp is not authenticated. Please scan the QR code below.'
                }
              </p>
            </div>
            <Badge variant={getStatusVariant()}>
              {connectionState}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Error Alert */}
      {error && (
        <Card className="border-l-4 border-l-red-500">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <AlertCircle className="h-5 w-5 text-red-500" />
              <p className="text-red-700 dark:text-red-400">{error}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Metrics Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => (
          <MetricCard key={index} stat={stat} isLoading={isLoading} />
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid gap-6 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Message Activity</CardTitle>
            <CardDescription>
              Daily message volume over the past week
            </CardDescription>
          </CardHeader>
          <CardContent>
            <SimpleLineChart data={messageChartData} height={200} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Monthly Overview</CardTitle>
            <CardDescription>
              Message activity by month
            </CardDescription>
          </CardHeader>
          <CardContent>
            <SimpleBarChart data={activityChartData} height={200} />
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* QR Code Section */}
        {!isAuthenticated && (
          <Card>
            <CardHeader>
              <CardTitle>WhatsApp Authentication</CardTitle>
              <CardDescription>
                Scan the QR code with your WhatsApp mobile app to connect
              </CardDescription>
            </CardHeader>
            <CardContent>
              <QRCodeSection
                qrCode={qrCode}
                isLoading={isLoadingQR}
                onRefresh={requestQRCode}
              />
            </CardContent>
          </Card>
        )}

        {/* Recent Messages */}
        <Card className={!isAuthenticated ? "lg:col-span-1" : "lg:col-span-2"}>
          <CardHeader>
            <CardTitle>Recent Messages</CardTitle>
            <CardDescription>
              Latest messages sent and received
            </CardDescription>
          </CardHeader>
          <CardContent>
            {recentMessages.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Contact</TableHead>
                    <TableHead>Message</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Time</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recentMessages.map((message, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">
                        {message.from || message.to || 'Unknown'}
                      </TableCell>
                      <TableCell className="max-w-xs truncate">
                        {message.body || message.text || 'Media message'}
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {message.fromMe ? 'Sent' : 'Received'}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-muted-foreground">
                        {new Date(message.timestamp).toLocaleTimeString()}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <Send className="h-12 w-12 text-muted-foreground mb-4" />
                <p className="text-muted-foreground">No messages yet</p>
                <p className="text-sm text-muted-foreground">
                  Messages will appear here once you start using the API
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default ModernDashboardPage
