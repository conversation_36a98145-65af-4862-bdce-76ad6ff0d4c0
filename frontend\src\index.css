@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 220 8.9% 46.1%;
    --card: 0 0% 100%;
    --card-foreground: 220 8.9% 46.1%;
    --popover: 0 0% 100%;
    --popover-foreground: 220 8.9% 46.1%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 0 0% 98%;
    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 220.9 39.3% 11%;
    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;
    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 217.2 91.2% 59.8%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 220.9 39.3% 11%;
    --foreground: 220 14.3% 95.9%;
    --card: 220.9 39.3% 11%;
    --card-foreground: 220 14.3% 95.9%;
    --popover: 220.9 39.3% 11%;
    --popover-foreground: 220 14.3% 95.9%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 220.9 39.3% 11%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 220 14.3% 95.9%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --accent: 215 27.9% 16.9%;
    --accent-foreground: 220 14.3% 95.9%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: 'Inter', 'Roboto', 'Helvetica', 'Arial', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}

#root {
  min-height: 100vh;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Loading spinner */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #25D366;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Message bubble styles */
.message-bubble {
  max-width: 70%;
  padding: 8px 12px;
  border-radius: 18px;
  margin: 4px 0;
  word-wrap: break-word;
}

.message-bubble.sent {
  background-color: #dcf8c6;
  margin-left: auto;
  border-bottom-right-radius: 4px;
}

.message-bubble.received {
  background-color: #ffffff;
  margin-right: auto;
  border-bottom-left-radius: 4px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.message-time {
  font-size: 0.75rem;
  color: #666;
  margin-top: 4px;
  text-align: right;
}

/* QR Code container */
.qr-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.qr-code {
  border: 2px solid #25D366;
  border-radius: 8px;
  padding: 16px;
  background: white;
}

/* Status indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-indicator.connected {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.status-indicator.disconnected {
  background-color: #ffebee;
  color: #c62828;
}

.status-indicator.connecting {
  background-color: #fff3e0;
  color: #ef6c00;
}

/* Chat list styles */
.chat-list-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.chat-list-item:hover {
  background-color: #f5f5f5;
}

.chat-list-item.active {
  background-color: #e3f2fd;
}

.chat-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  margin-right: 12px;
  background-color: #25D366;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
}

.chat-info {
  flex: 1;
  min-width: 0;
}

.chat-name {
  font-weight: 500;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chat-last-message {
  color: #666;
  font-size: 0.875rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chat-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.chat-time {
  color: #666;
  font-size: 0.75rem;
}

.unread-count {
  background-color: #25D366;
  color: white;
  border-radius: 50%;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: bold;
}

/* Form styles */
.form-container {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.form-field {
  margin-bottom: 16px;
}

/* Responsive design */
@media (max-width: 768px) {
  .message-bubble {
    max-width: 85%;
  }
  
  .chat-list-item {
    padding: 8px 12px;
  }
  
  .chat-avatar {
    width: 40px;
    height: 40px;
    margin-right: 8px;
  }
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

/* Utility classes */
.text-center {
  text-align: center;
}

.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.full-height {
  height: 100vh;
}

.no-scroll {
  overflow: hidden;
}
