import React, { useEffect, useState } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { cn } from '@/lib/utils'
import { TooltipProvider } from '@/components/ui/tooltip'

import { useAuthStore } from './stores/authStore'
import { useSocketStore } from './stores/socketStore'
import { useWhatsAppStore } from './stores/whatsappStore'

import ModernNavbar from './components/Layout/ModernNavbar'
import ModernSidebar from './components/Layout/ModernSidebar'
import LoadingScreen from './components/Common/LoadingScreen'

// Pages
import LoginPage from './pages/LoginPage'
import ModernDashboardPage from './pages/ModernDashboardPage'
import MessagesPage from './pages/MessagesPage'
import ContactsPage from './pages/ContactsPage'
import GroupsPage from './pages/GroupsPage'
import ScheduledPage from './pages/ScheduledPage'
import SettingsPage from './pages/SettingsPage'

function ModernApp() {
  const { isAuthenticated, isLoading, checkAuth } = useAuthStore()
  const { connect, disconnect } = useSocketStore()
  const { initialize } = useWhatsAppStore()
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)

  useEffect(() => {
    // Check authentication status on app load
    checkAuth()
  }, [checkAuth])

  useEffect(() => {
    if (isAuthenticated) {
      // Connect to WebSocket when authenticated
      connect()
      // Initialize WhatsApp status monitoring
      initialize()
    } else {
      // Disconnect WebSocket when not authenticated
      disconnect()
    }

    return () => {
      disconnect()
    }
  }, [isAuthenticated, connect, disconnect, initialize])

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  if (isLoading) {
    return <LoadingScreen />
  }

  if (!isAuthenticated) {
    return (
      <TooltipProvider>
        <Routes>
          <Route path="/login" element={<LoginPage />} />
          <Route path="*" element={<Navigate to="/login" replace />} />
        </Routes>
      </TooltipProvider>
    )
  }

  return (
    <TooltipProvider>
      <div className="flex h-screen bg-background">
        {/* Sidebar */}
        <aside className={cn(
          "hidden md:flex flex-col border-r border-border transition-all duration-300 ease-in-out",
          sidebarCollapsed ? "w-16" : "w-64"
        )}>
          <ModernSidebar 
            collapsed={sidebarCollapsed} 
            onToggle={toggleSidebar}
          />
        </aside>

        {/* Main Content */}
        <div className="flex flex-1 flex-col overflow-hidden">
          {/* Navbar */}
          <ModernNavbar 
            onToggleSidebar={toggleSidebar}
            sidebarCollapsed={sidebarCollapsed}
          />

          {/* Page Content */}
          <main className="flex-1 overflow-auto bg-muted/30">
            <div className="container mx-auto max-w-7xl">
              <Routes>
                <Route path="/" element={<ModernDashboardPage />} />
                <Route path="/dashboard" element={<ModernDashboardPage />} />
                <Route path="/messages" element={<MessagesPage />} />
                <Route path="/contacts" element={<ContactsPage />} />
                <Route path="/groups" element={<GroupsPage />} />
                <Route path="/scheduled" element={<ScheduledPage />} />
                <Route path="/settings" element={<SettingsPage />} />
                <Route path="*" element={<Navigate to="/dashboard" replace />} />
              </Routes>
            </div>
          </main>
        </div>
      </div>
    </TooltipProvider>
  )
}

export default ModernApp
