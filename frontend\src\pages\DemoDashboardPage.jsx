import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { Separator } from '@/components/ui/separator'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { SimpleLineChart, SimpleBarChart } from '@/components/ui/simple-chart'
import {
  MessageSquare,
  Users,
  UsersRound,
  Activity,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock,
  Send,
} from 'lucide-react'

const DemoDashboardPage = () => {
  const [isLoading, setIsLoading] = useState(false)

  const handleRefresh = () => {
    setIsLoading(true)
    setTimeout(() => setIsLoading(false), 1000)
  }

  const stats = [
    {
      title: 'Total Messages',
      value: 1247,
      change: '+12%',
      trend: 'up',
      icon: MessageSquare,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 dark:bg-blue-950',
    },
    {
      title: 'Active Contacts',
      value: 89,
      change: '+5%',
      trend: 'up',
      icon: Users,
      color: 'text-green-600',
      bgColor: 'bg-green-50 dark:bg-green-950',
    },
    {
      title: 'Group Chats',
      value: 12,
      change: '+2%',
      trend: 'up',
      icon: UsersRound,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50 dark:bg-purple-950',
    },
    {
      title: 'Messages Today',
      value: 34,
      change: '-3%',
      trend: 'down',
      icon: Activity,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50 dark:bg-orange-950',
    },
  ]

  const messageChartData = [
    { label: 'Mon', value: 12 },
    { label: 'Tue', value: 19 },
    { label: 'Wed', value: 8 },
    { label: 'Thu', value: 15 },
    { label: 'Fri', value: 22 },
    { label: 'Sat', value: 18 },
    { label: 'Sun', value: 14 },
  ]

  const activityChartData = [
    { label: 'Jan', value: 65 },
    { label: 'Feb', value: 78 },
    { label: 'Mar', value: 52 },
    { label: 'Apr', value: 89 },
    { label: 'May', value: 95 },
    { label: 'Jun', value: 87 },
  ]

  const recentMessages = [
    {
      from: 'John Doe',
      body: 'Hello, how are you?',
      fromMe: false,
      timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString(),
    },
    {
      to: 'Jane Smith',
      body: 'Meeting scheduled for tomorrow',
      fromMe: true,
      timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
    },
    {
      from: 'Support Team',
      body: 'Your request has been processed',
      fromMe: false,
      timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
    },
    {
      to: 'Marketing Group',
      body: 'Campaign results are ready',
      fromMe: true,
      timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString(),
    },
    {
      from: 'Alice Johnson',
      body: 'Thanks for the update!',
      fromMe: false,
      timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString(),
    },
  ]

  const MetricCard = ({ stat, isLoading }) => {
    const Icon = stat.icon
    const TrendIcon = stat.trend === 'up' ? TrendingUp : TrendingDown
    
    if (isLoading) {
      return (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-8 w-16" />
                <Skeleton className="h-3 w-12" />
              </div>
              <Skeleton className="h-12 w-12 rounded-lg" />
            </div>
          </CardContent>
        </Card>
      )
    }

    return (
      <Card className="transition-all duration-200 ease-in-out hover:shadow-md">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">
                {stat.title}
              </p>
              <p className="text-3xl font-bold">
                {stat.value.toLocaleString()}
              </p>
              <div className="flex items-center gap-1 text-xs">
                <TrendIcon className={cn(
                  "h-3 w-3",
                  stat.trend === 'up' ? "text-green-600" : "text-red-600"
                )} />
                <span className={cn(
                  "font-medium",
                  stat.trend === 'up' ? "text-green-600" : "text-red-600"
                )}>
                  {stat.change}
                </span>
                <span className="text-muted-foreground">from last month</span>
              </div>
            </div>
            <div className={cn(
              "flex h-12 w-12 items-center justify-center rounded-lg",
              stat.bgColor
            )}>
              <Icon className={cn("h-6 w-6", stat.color)} />
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Monitor your WhatsApp API gateway status and activity
          </p>
        </div>
        <Button onClick={handleRefresh} disabled={isLoading}>
          <RefreshCw className={cn("mr-2 h-4 w-4", isLoading && "animate-spin")} />
          Refresh
        </Button>
      </div>

      {/* Status Alert */}
      <Card className="border-l-4 border-l-green-500">
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <CheckCircle className="h-5 w-5 text-green-500" />
            <div className="flex-1">
              <p className="font-medium">WhatsApp Ready</p>
              <p className="text-sm text-muted-foreground">
                WhatsApp is connected and ready to send/receive messages
              </p>
            </div>
            <Badge variant="default">
              CONNECTED
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Metrics Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => (
          <MetricCard key={index} stat={stat} isLoading={isLoading} />
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid gap-6 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Message Activity</CardTitle>
            <CardDescription>
              Daily message volume over the past week
            </CardDescription>
          </CardHeader>
          <CardContent>
            <SimpleLineChart data={messageChartData} height={200} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Monthly Overview</CardTitle>
            <CardDescription>
              Message activity by month
            </CardDescription>
          </CardHeader>
          <CardContent>
            <SimpleBarChart data={activityChartData} height={200} />
          </CardContent>
        </Card>
      </div>

      {/* Recent Messages */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Messages</CardTitle>
          <CardDescription>
            Latest messages sent and received
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Contact</TableHead>
                <TableHead>Message</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Time</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {recentMessages.map((message, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">
                    {message.from || message.to || 'Unknown'}
                  </TableCell>
                  <TableCell className="max-w-xs truncate">
                    {message.body || 'Media message'}
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {message.fromMe ? 'Sent' : 'Received'}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-muted-foreground">
                    {new Date(message.timestamp).toLocaleTimeString()}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}

export default DemoDashboardPage
