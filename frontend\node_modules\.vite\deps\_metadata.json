{"hash": "5981726f", "configHash": "c635a857", "lockfileHash": "5d73fe35", "browserHash": "957068b9", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "b70176bc", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "e8e2438d", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "d0ecffe7", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "36ac410c", "needsInterop": true}, "@radix-ui/react-avatar": {"src": "../../@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "86c5135e", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "0969d840", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "8b04c6c4", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "940e79ff", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "6e65a578", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "2cc51840", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "ac7c4c33", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "66a5ea59", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "d3d2cd9f", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "c06adbac", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "628867b3", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "14628d68", "needsInterop": false}, "date-fns": {"src": "../../date-fns/esm/index.js", "file": "date-fns.js", "fileHash": "63977509", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "d207403e", "needsInterop": false}, "qrcode.react": {"src": "../../qrcode.react/lib/esm/index.js", "file": "qrcode__react.js", "fileHash": "81b59dcd", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "9a586918", "needsInterop": true}, "react-hook-form": {"src": "../../react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "baff3a4a", "needsInterop": false}, "react-hot-toast": {"src": "../../react-hot-toast/dist/index.mjs", "file": "react-hot-toast.js", "fileHash": "8fa003cd", "needsInterop": false}, "react-query": {"src": "../../react-query/es/index.js", "file": "react-query.js", "fileHash": "beee8ee9", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "b59c2f56", "needsInterop": false}, "socket.io-client": {"src": "../../socket.io-client/build/esm/index.js", "file": "socket__io-client.js", "fileHash": "985b9c6b", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "449ce3fe", "needsInterop": false}, "zustand": {"src": "../../zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "ec90c6d4", "needsInterop": false}, "zustand/middleware": {"src": "../../zustand/esm/middleware.mjs", "file": "zustand_middleware.js", "fileHash": "e1ca1af1", "needsInterop": false}}, "chunks": {"chunk-S27VPZAG": {"file": "chunk-S27VPZAG.js"}, "chunk-TFX6JGDG": {"file": "chunk-TFX6JGDG.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-4FTWOKSW": {"file": "chunk-4FTWOKSW.js"}, "chunk-WD2D25B4": {"file": "chunk-WD2D25B4.js"}, "chunk-CAVCFQAX": {"file": "chunk-CAVCFQAX.js"}, "chunk-KYJY7BOJ": {"file": "chunk-KYJY7BOJ.js"}, "chunk-S5QMYVV3": {"file": "chunk-S5QMYVV3.js"}, "chunk-Y5GUZ6YM": {"file": "chunk-Y5GUZ6YM.js"}, "chunk-PA26OJAO": {"file": "chunk-PA26OJAO.js"}, "chunk-64UQNONV": {"file": "chunk-64UQNONV.js"}, "chunk-ZL3ZHN7B": {"file": "chunk-ZL3ZHN7B.js"}, "chunk-4FS7H3TQ": {"file": "chunk-4FS7H3TQ.js"}, "chunk-EJ2PZ5KR": {"file": "chunk-EJ2PZ5KR.js"}, "chunk-ELMVTMPM": {"file": "chunk-ELMVTMPM.js"}, "chunk-R6R6T6DH": {"file": "chunk-R6R6T6DH.js"}, "chunk-PJEEZAML": {"file": "chunk-PJEEZAML.js"}, "chunk-6PXSGDAH": {"file": "chunk-6PXSGDAH.js"}, "chunk-DRWLMN53": {"file": "chunk-DRWLMN53.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}