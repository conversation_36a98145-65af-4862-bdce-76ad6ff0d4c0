import React from 'react'
import { cn } from '@/lib/utils'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Alert } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import {
  LogOut,
  RefreshCw,
  Download,
  Upload,
  Settings,
  User,
  Shield,
  Database,
} from 'lucide-react'

import { useAuthStore } from '../stores/authStore'
import { useWhatsAppStore } from '../stores/whatsappStore'

const SettingsPage = () => {
  const { user, logout } = useAuthStore()
  const { isAuthenticated, isReady } = useWhatsAppStore()

  const handleLogout = () => {
    logout()
  }

  const handleExportData = () => {
    console.log('Exporting data...')
  }

  const handleImportData = () => {
    console.log('Importing data...')
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
        <p className="text-muted-foreground">
          Configure your WhatsApp API gateway settings
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Account Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Account Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">Username</p>
                  <p className="text-sm text-muted-foreground">
                    {user?.username || 'Not available'}
                  </p>
                </div>
              </div>

              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">Role</p>
                  <p className="text-sm text-muted-foreground">
                    {user?.role || 'Not available'}
                  </p>
                </div>
              </div>

              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">Session Status</p>
                  <p className="text-sm text-muted-foreground">
                    {isAuthenticated ? 'Active' : 'Inactive'}
                  </p>
                </div>
              </div>
            </div>

            <Separator />

            <Button
              variant="outline"
              onClick={handleLogout}
              className="w-full text-red-600 border-red-200 hover:bg-red-50"
            >
              <LogOut className="h-4 w-4 mr-2" />
              Logout
            </Button>
          </CardContent>
        </Card>

        {/* WhatsApp Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              WhatsApp Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label className="text-base">Auto-download Media</Label>
                <p className="text-sm text-muted-foreground">
                  Automatically download received media files
                </p>
              </div>
              <Switch defaultChecked />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label className="text-base">Message Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Show browser notifications for new messages
                </p>
              </div>
              <Switch defaultChecked />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label className="text-base">Auto-reply</Label>
                <p className="text-sm text-muted-foreground">
                  Enable automatic replies when away
                </p>
              </div>
              <Switch />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Data Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Data Management
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <Button
                variant="outline"
                onClick={handleExportData}
                className="w-full"
              >
                <Download className="h-4 w-4 mr-2" />
                Export Data
              </Button>
              <Button
                variant="outline"
                onClick={handleImportData}
                className="w-full"
              >
                <Upload className="h-4 w-4 mr-2" />
                Import Data
              </Button>
            </div>

            <Alert className="border-blue-200 bg-blue-50 text-blue-800">
              Export includes contacts, message history, and scheduled messages.
              Import will merge with existing data.
            </Alert>
          </div>
        </CardContent>
      </Card>

      {/* System Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            System Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex justify-between items-center">
            <div>
              <p className="font-medium">Application Version</p>
              <p className="text-sm text-muted-foreground">1.0.0</p>
            </div>
          </div>

          <div className="flex justify-between items-center">
            <div>
              <p className="font-medium">WhatsApp Status</p>
              <p className="text-sm text-muted-foreground">
                {isReady ? 'Connected and Ready' : 'Not Ready'}
              </p>
            </div>
          </div>

          <div className="flex justify-between items-center">
            <div>
              <p className="font-medium">Last Updated</p>
              <p className="text-sm text-muted-foreground">
                {new Date().toLocaleDateString()}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default SettingsPage
