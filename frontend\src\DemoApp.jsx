import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { TooltipProvider } from '@/components/ui/tooltip'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'
import {
  LayoutDashboard,
  MessageSquare,
  Users,
  UsersRound,
  Calendar,
  Settings,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react'

import DemoDashboardPage from './pages/DemoDashboardPage'

const menuItems = [
  {
    text: 'Dashboard',
    icon: LayoutDashboard,
    path: '/dashboard',
    description: 'Overview and analytics'
  },
  {
    text: 'Messages',
    icon: MessageSquare,
    path: '/messages',
    description: 'Send and manage messages'
  },
  {
    text: 'Contacts',
    icon: Users,
    path: '/contacts',
    description: 'Manage your contacts'
  },
  {
    text: 'Groups',
    icon: UsersRound,
    path: '/groups',
    description: 'Manage group chats'
  },
  {
    text: 'Scheduled',
    icon: Calendar,
    path: '/scheduled',
    description: 'Scheduled messages'
  },
]

const bottomMenuItems = [
  {
    text: 'Settings',
    icon: Settings,
    path: '/settings',
    description: 'Application settings'
  },
]

// Demo Sidebar Component (no router dependency)
const DemoSidebar = ({ collapsed = false, onToggle }) => {
  const [activeItem, setActiveItem] = useState('/dashboard')

  const handleNavigation = (path) => {
    setActiveItem(path)
    console.log('Navigate to:', path)
  }

  const isActive = (path) => {
    return activeItem === path ||
           (path === '/dashboard' && activeItem === '/')
  }

  const SidebarItem = ({ item, isBottom = false }) => {
    const Icon = item.icon
    const active = isActive(item.path)

    const button = (
      <Button
        variant={active ? "secondary" : "ghost"}
        className={cn(
          "w-full justify-start gap-3 h-12 px-3 transition-all duration-200 ease-in-out",
          collapsed ? "px-2" : "px-3",
          active && "bg-primary/10 text-primary border-r-2 border-primary",
          !active && "hover:bg-accent hover:text-accent-foreground"
        )}
        onClick={() => handleNavigation(item.path)}
      >
        <Icon className={cn("h-5 w-5 flex-shrink-0", active && "text-primary")} />
        {!collapsed && (
          <span className="font-medium text-sm">{item.text}</span>
        )}
      </Button>
    )

    if (collapsed) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              {button}
            </TooltipTrigger>
            <TooltipContent side="right" className="ml-2">
              <p className="font-medium">{item.text}</p>
              <p className="text-xs text-muted-foreground">{item.description}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )
    }

    return button
  }

  return (
    <div className={cn(
      "flex flex-col h-full bg-card border-r border-border transition-all duration-300 ease-in-out",
      collapsed ? "w-16" : "w-64"
    )}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        {!collapsed && (
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <MessageSquare className="h-5 w-5 text-primary-foreground" />
            </div>
            <div>
              <h2 className="font-semibold text-sm">WhatsApp API</h2>
              <p className="text-xs text-muted-foreground">Gateway</p>
            </div>
          </div>
        )}

        {onToggle && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggle}
            className="h-8 w-8 p-0"
          >
            {collapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
        )}
      </div>

      {/* Navigation */}
      <div className="flex-1 flex flex-col justify-between p-2">
        <nav className="space-y-1">
          {menuItems.map((item) => (
            <SidebarItem key={item.path} item={item} />
          ))}
        </nav>

        <div className="space-y-2">
          <Separator />
          {bottomMenuItems.map((item) => (
            <SidebarItem key={item.path} item={item} isBottom />
          ))}
        </div>
      </div>
    </div>
  )
}

// Mock navbar component for demo
const DemoNavbar = ({ onToggleSidebar, sidebarCollapsed }) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [darkMode, setDarkMode] = useState(false)

  const toggleDarkMode = () => {
    setDarkMode(!darkMode)
    document.documentElement.classList.toggle('dark')
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex h-16 items-center justify-between px-4">
        {/* Left Section */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleSidebar}
            className="hidden md:flex"
          >
            <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </Button>

          <div className="relative hidden sm:block">
            <svg className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <input
              placeholder="Search..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 w-64 h-9 px-3 py-2 text-sm bg-muted/50 border-0 rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
            />
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2 px-3 py-1.5 rounded-md bg-muted/50">
            <svg className="h-4 w-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
            </svg>
            <span className="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full dark:bg-green-900 dark:text-green-200">
              Ready
            </span>
          </div>

          <div className="h-6 w-px bg-border"></div>

          <Button variant="ghost" size="sm" onClick={toggleDarkMode}>
            {darkMode ? (
              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            ) : (
              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
              </svg>
            )}
          </Button>

          <Button variant="ghost" size="sm" className="relative">
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM9 7H4l5-5v5zm6 10V7a1 1 0 00-1-1H5a1 1 0 00-1 1v10a1 1 0 001 1h9a1 1 0 001-1z" />
            </svg>
            <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full text-xs"></span>
          </Button>

          <Button variant="ghost" className="relative h-9 w-9 rounded-full">
            <div className="flex h-9 w-9 items-center justify-center bg-primary text-primary-foreground text-sm font-medium rounded-full">
              D
            </div>
          </Button>
        </div>
      </div>
    </header>
  )
}

function DemoApp() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  return (
    <TooltipProvider>
      <div className="flex h-screen bg-background">
        {/* Sidebar */}
        <aside className={cn(
          "hidden md:flex flex-col border-r border-border transition-all duration-300 ease-in-out",
          sidebarCollapsed ? "w-16" : "w-64"
        )}>
          <DemoSidebar
            collapsed={sidebarCollapsed}
            onToggle={toggleSidebar}
          />
        </aside>

        {/* Main Content */}
        <div className="flex flex-1 flex-col overflow-hidden">
          {/* Navbar */}
          <DemoNavbar 
            onToggleSidebar={toggleSidebar}
            sidebarCollapsed={sidebarCollapsed}
          />

          {/* Page Content */}
          <main className="flex-1 overflow-auto bg-muted/30">
            <div className="container mx-auto max-w-7xl">
              <DemoDashboardPage />
            </div>
          </main>
        </div>
      </div>
    </TooltipProvider>
  )
}

export default DemoApp
